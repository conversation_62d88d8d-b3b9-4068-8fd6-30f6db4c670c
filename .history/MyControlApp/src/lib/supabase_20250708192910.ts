import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import { Database } from '../types/supabase';

// IMPORTANT: Replace with your own Supabase URL and Anon Key
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://msplpjnrmbgnjuvygbyf.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1zcGxwam5ybWJnbmp1dnlnYnlmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NjUxMjgsImV4cCI6MjA2NzU0MTEyOH0.FkxpHiiM92m8-1C3pbAFDBoex6s83aMqY_1LkR9NOBo';

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);