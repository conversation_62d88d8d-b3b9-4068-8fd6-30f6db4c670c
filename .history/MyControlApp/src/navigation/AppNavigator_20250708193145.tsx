import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

// TODO: Import screens
// import LoginScreen from '../screens/LoginScreen';
// import DashboardScreen from '../screens/DashboardScreen';

const Stack = createNativeStackNavigator();

function AppNavigator() {
  const user = null; // TODO: Replace with auth state from store

  return (
    <NavigationContainer>
      <Stack.Navigator>
        {user ? (
          <Stack.Screen name="Dashboard" component={() => null} /> // Placeholder
        ) : (
          <Stack.Screen name="Login" component={() => null} /> // Placeholder
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default AppNavigator;