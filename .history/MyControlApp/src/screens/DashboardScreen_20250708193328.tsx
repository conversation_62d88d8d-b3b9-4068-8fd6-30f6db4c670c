import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button, Text } from 'react-native-paper';

function DashboardScreen() {
  return (
    <View style={styles.container}>
      <Text variant="headlineLarge">Dashboard</Text>
      <Text>Welcome to the parent dashboard!</Text>
      <Button mode="contained" onPress={() => console.log('Lock device pressed')}>
        Lock Kid's Device
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
});

export default DashboardScreen;