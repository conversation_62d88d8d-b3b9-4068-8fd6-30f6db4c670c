export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      // This will be populated by Supabase CLI
    }
    Views: {
      // This will be populated by Supabase CLI
    }
    Functions: {
      // This will be populated by Supabase CLI
    }
    Enums: {
      // This will be populated by Supabase CLI
    }
    CompositeTypes: {
      // This will be populated by Supabase CLI
    }
  }
}