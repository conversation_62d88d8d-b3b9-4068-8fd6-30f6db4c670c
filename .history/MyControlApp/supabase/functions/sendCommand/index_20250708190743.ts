import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

console.log(`Function "send-command" up and running!`);

// Define the shape of the incoming request body
interface CommandPayload {
  device_id: string;
  command: 'lock' | 'unlock';
}

Deno.serve(async (req) => {
  // This is needed if you're planning to invoke your function from a browser.
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { device_id, command }: CommandPayload = await req.json();

    // Create a Supabase client with the user's authorization
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      // This way your row-level-security (RLS) policies are applied.
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    // 1. Verify the user is a 'parent' and belongs to the same family as the device's owner.
    const { data: { user } } = await supabaseClient.auth.getUser();
    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      });
    }

    // Get the profile of the calling user
    const { data: callerProfile, error: callerProfileError } = await supabaseClient
      .from('profiles')
      .select('role, family_id')
      .eq('id', user.id)
      .single();

    if (callerProfileError || !callerProfile) {
      return new Response(JSON.stringify({ error: 'Caller profile not found.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    if (callerProfile.role !== 'parent') {
      return new Response(JSON.stringify({ error: 'Only parents can send commands.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      });
    }

    // Get the device and its owner's profile to verify family connection
    const { data: deviceData, error: deviceError } = await supabaseClient
      .from('devices')
      .select(`
        child_id,
        profiles ( family_id )
      `)
      .eq('id', device_id)
      .single();

    if (deviceError || !deviceData) {
        return new Response(JSON.stringify({ error: 'Device not found.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404,
        });
    }
    
    // @ts-ignore: Edge functions do not have full TS support for relation joins yet
    if (deviceData.profiles.family_id !== callerProfile.family_id) {
        return new Response(JSON.stringify({ error: 'Device is not in the same family.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
        });
    }

    // 2. Update the device status
    const newStatus = command === 'lock' ? 'locked' : 'active';
    const { data, error } = await supabaseClient
      .from('devices')
      .update({ status: newStatus })
      .eq('id', device_id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});