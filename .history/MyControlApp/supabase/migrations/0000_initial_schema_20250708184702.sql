-- Create custom enum types for user roles and device statuses
create type public.user_role as enum ('parent', 'child');
create type public.device_status as enum ('active', 'locked');

-- Create the families table to group users
create table public.families (
    id uuid not null default gen_random_uuid() primary key,
    invite_code text not null unique,
    created_by uuid not null references auth.users(id),
    created_at timestamp with time zone not null default now()
);
comment on table public.families is 'Stores family groups and their unique invite codes.';

-- Create the profiles table to store user-specific data
create table public.profiles (
    id uuid not null primary key references auth.users(id) on delete cascade,
    username text,
    role user_role not null,
    family_id uuid references public.families(id) on delete cascade,
    updated_at timestamp with time zone
);
comment on table public.profiles is 'Stores public profile data for each user, linking to a family.';

-- Create the devices table for child devices
create table public.devices (
    id uuid not null default gen_random_uuid() primary key,
    child_id uuid not null references public.profiles(id) on delete cascade,
    device_name text,
    status device_status not null default 'active',
    created_at timestamp with time zone not null default now()
);
comment on table public.devices is 'Stores information about child devices.';

-- Create the app_usage table to log app usage
create table public.app_usage (
    id bigint generated by default as identity primary key,
    device_id uuid not null references public.devices(id) on delete cascade,
    app_identifier text not null,
    usage_duration_seconds integer not null,
    date date not null,
    created_at timestamp with time zone not null default now()
);
comment on table public.app_usage is 'Logs application usage statistics for child devices.';

-- Create the app_rules table to set limits on app usage
create table public.app_rules (
    id bigint generated by default as identity primary key,
    device_id uuid not null references public.devices(id) on delete cascade,
    app_identifier text not null,
    time_limit_minutes integer not null,
    created_at timestamp with time zone not null default now(),
    unique(device_id, app_identifier)
);
comment on table public.app_rules is 'Defines usage time limits for specific applications on devices.';
